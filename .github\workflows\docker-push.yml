name: Docker Build and Push to DOCR

on:
  workflow_call:
    secrets:
      DIGITALOCEAN_ACCESS_TOKEN:
        required: true
    outputs:
      image-tag:
        description: "Docker image tag that was built and pushed"
        value: ${{ jobs.build-and-push.outputs.image-tag }}

jobs:
  build-and-push:
    name: Build and Push Docker Image
    runs-on: [self-hosted, Linux]
    outputs:
      image-tag: ${{ steps.generate-tag.outputs.tag }}
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3
      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
      - name: Generate Docker Tag
        id: generate-tag
        run: |
          SHORT_SHA=$(echo ${{ github.sha }} | cut -c1-7)
          TIMESTAMP=$(date -u +"%Y%m%d-%H%M%S")
          TAG="${SHORT_SHA}-${TIMESTAMP}"
          echo "tag=${TAG}" >> $GITHUB_OUTPUT
          echo "Generated tag: ${TAG}"
      - name: Set environment (default to spring)
        run: |
          echo "ENVIRONMENT=${ENVIRONMENT:-spring}" >> $GITHUB_ENV
      - name: Authenticate Docker with DigitalOcean Container Registry (DOCR)
        run: doctl registry login
      - name: Build Docker Image
        run: docker build --build-arg ENV_FILE=${{ env.ENV_FILE }} -t ai-react-frontend .
        env:
          ENV_FILE: .env.spring
      - name: Tag Docker Image for DOCR
        run: |
          IMAGE_TAG="${{ steps.generate-tag.outputs.tag }}"
          IMAGE_LATEST="registry.digitalocean.com/doks-registry/ai-react-frontend:latest"
          IMAGE_TAGGED="registry.digitalocean.com/doks-registry/ai-react-frontend:${IMAGE_TAG}"
          docker tag ai-react-frontend $IMAGE_LATEST
          docker tag ai-react-frontend $IMAGE_TAGGED
          echo "Tagged images: $IMAGE_LATEST and $IMAGE_TAGGED"
      - name: Push Images to DOCR
        run: |
          IMAGE_TAG="${{ steps.generate-tag.outputs.tag }}"
          docker push registry.digitalocean.com/doks-registry/ai-react-frontend:latest
          docker push registry.digitalocean.com/doks-registry/ai-react-frontend:${IMAGE_TAG}
          echo "Pushed both latest and tagged images"