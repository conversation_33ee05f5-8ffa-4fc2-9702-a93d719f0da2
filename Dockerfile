# Stage 1: Build the React app
FROM node:18-alpine AS builder

WORKDIR /app

COPY package*.json ./
RUN npm install

ARG ENV_FILE=.env.spring
COPY . .
COPY ${ENV_FILE} .env

ARG BUILD_SCRIPT=build:spring
RUN npm run $BUILD_SCRIPT

# Stage 2: Serve the built app with a static server
FROM nginx:alpine

# Copy built assets from builder
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy custom nginx config (optional)
# COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

# Create a non-root user to run nginx
RUN getent group nginx || addgroup -g 1001 -S nginx && \
    id -u nginx 2>/dev/null || adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# Change ownership of nginx directories to the nginx user
RUN chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    chown -R nginx:nginx /usr/share/nginx/html && \
    touch /var/run/nginx.pid && \
    chown -R nginx:nginx /var/run/nginx.pid

# Add a non-root user
RUN adduser -D -h /home/<USER>
USER appuser

CMD ["nginx", "-g", "daemon off;"]